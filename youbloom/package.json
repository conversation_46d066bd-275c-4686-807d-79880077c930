{"name": "youbloom", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.13", "@tanstack/react-query": "^5.89.0", "axios": "^1.12.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.9.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0", "vite": "^7.1.6"}}