@import "tailwindcss";

/*========= reusable =========*/
.nunito{
  font-family: "Nunito",sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}
.caprasimo{
  font-family: "<PERSON><PERSON><PERSON>",serif;
  font-weight: 400;
  font-size: normal;
}

.loader {
  width: 50px;
  --b: 8px; 
  aspect-ratio: 1;
  border-radius: 50%;
  padding: 1px;
  background: conic-gradient(#0000 10%,#000) content-box;
  -webkit-mask:
    repeating-conic-gradient(#0000 0deg,#000 1deg 20deg,#0000 21deg 36deg),
    radial-gradient(farthest-side,#0000 calc(100% - var(--b) - 1px),#000 calc(100% - var(--b)));
  -webkit-mask-composite: destination-in;
          mask-composite: intersect;
  animation:l4 1s infinite steps(10);
}
@keyframes l4 {to{transform: rotate(1turn)}}

.sideScrollbar::-webkit-scrollbar{
  width:8px;
}

.sideScrollbar::-webkit-scrollbar-track{
  background-color: #ecf0f1;
  border-radius: 100px;
}

.sideScrollbar::-webkit-scrollbar-thumb{
  background-color:rgba(0,0,0,0.5);
  border-radius: 100px;
}
/*========= bannerImg =========*/
.bannerBg{
  background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.2)), url("./assets/musicbg.jpg");
}